using LiteDB;
using System;
using System.IO;
using System.Linq;
using Weightdb;
using LiteTest.Lite;

namespace DbViewer
{
    /// <summary>
    /// 纯控制台版本的 LiteDB 数据库查看器
    /// </summary>
    public class ConsoleDbViewer
    {
        /// <summary>
        /// 主程序入口
        /// </summary>
        public static void Main(string[] args)
        {
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            
            string dbPath = "productdata-log_2025年08月13日15时51分58秒.db";
            
            // 如果提供了命令行参数，使用第一个参数作为数据库路径
            if (args.Length > 0)
            {
                dbPath = args[0];
            }
            
            Console.WriteLine("🗄️  LiteDB 数据库完整查看器 (C# 控制台版本)");
            Console.WriteLine("==========================================");
            Console.WriteLine();
            
            ViewDatabase(dbPath);
            
            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 读取并显示数据库内容
        /// </summary>
        /// <param name="dbPath">数据库文件路径</param>
        public static void ViewDatabase(string dbPath)
        {
            try
            {
                // 检查文件是否存在
                if (!File.Exists(dbPath))
                {
                    Console.WriteLine($"❌ 数据库文件不存在: {dbPath}");
                    Console.WriteLine("请确认文件路径是否正确。");
                    return;
                }

                Console.WriteLine("🔍 正在读取数据库...");
                Console.WriteLine($"📁 文件路径: {dbPath}");
                Console.WriteLine($"📊 文件大小: {new FileInfo(dbPath).Length / 1024.0:F2} KB");
                Console.WriteLine();

                // 尝试不同的连接方式
                var connectionString = $"Filename={dbPath};ReadOnly=true";

                try
                {
                    using (var db = new LiteDatabase(connectionString))
                    {
                        Console.WriteLine("✅ 数据库连接成功! (只读模式)");
                        Console.WriteLine();

                        // 获取所有集合名称
                        var collections = db.GetCollectionNames().ToList();
                        Console.WriteLine($"📋 数据库集合 (共 {collections.Count} 个):");

                        foreach (var collection in collections)
                        {
                            var count = db.GetCollection(collection).Count();
                            Console.WriteLine($"   • {collection}: {count} 条记录");
                        }
                        Console.WriteLine();

                        // 读取称重记录
                        ViewWeightRecords(db);

                        // 读取产品信息
                        ViewProductRecords(db);
                    }
                }
                catch (Exception ex2)
                {
                    Console.WriteLine($"❌ 只读模式也失败: {ex2.Message}");
                    Console.WriteLine("尝试使用兼容模式...");

                    // 尝试使用更宽松的设置
                    var settings = new ConnectionString(dbPath)
                    {
                        ReadOnly = true,
                        Upgrade = false
                    };

                    try
                    {
                        using (var db = new LiteDatabase(settings))
                        {
                            Console.WriteLine("✅ 数据库连接成功! (兼容模式)");
                            Console.WriteLine();

                            // 获取所有集合名称
                            var collections = db.GetCollectionNames().ToList();
                            Console.WriteLine($"📋 数据库集合 (共 {collections.Count} 个):");

                            foreach (var collection in collections)
                            {
                                var count = db.GetCollection(collection).Count();
                                Console.WriteLine($"   • {collection}: {count} 条记录");
                            }
                            Console.WriteLine();

                            // 读取称重记录
                            ViewWeightRecords(db);

                            // 读取产品信息
                            ViewProductRecords(db);
                        }
                    }
                    catch (Exception ex3)
                    {
                        Console.WriteLine($"❌ 兼容模式也失败: {ex3.Message}");
                        Console.WriteLine("数据库文件可能损坏或使用了不兼容的版本。");
                        Console.WriteLine("建议使用 Python 脚本进行文本解析查看。");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 读取数据库时发生错误: {ex.Message}");
                Console.WriteLine($"详细错误信息: {ex}");
            }
        }

        /// <summary>
        /// 查看称重记录
        /// </summary>
        private static void ViewWeightRecords(LiteDatabase db)
        {
            try
            {
                var weights = db.GetCollection<WeightItem>("weights");
                var weightRecords = weights.FindAll().OrderByDescending(x => x.Id).ToList();

                Console.WriteLine($"⚖️  === 称重记录 (weights) - 共 {weightRecords.Count} 条记录 ===");
                
                if (weightRecords.Count == 0)
                {
                    Console.WriteLine("   暂无称重记录");
                    Console.WriteLine();
                    return;
                }

                // 表头
                Console.WriteLine("ID".PadRight(5) + 
                                "产品编码".PadRight(12) + 
                                "型号".PadRight(15) + 
                                "序列号".PadRight(18) + 
                                "重量".PadRight(8) + 
                                "误差".PadRight(8) + 
                                "结果".PadRight(6) + 
                                "加工单号".PadRight(12) + 
                                "操作员".PadRight(10) + 
                                "称重时间");
                Console.WriteLine(new string('─', 120));

                // 显示前20条记录
                foreach (var record in weightRecords.Take(20))
                {
                    Console.WriteLine(
                        $"{record.Id}".PadRight(5) +
                        $"{record.Productcode ?? ""}".PadRight(12) +
                        $"{record.Model ?? ""}".PadRight(15) +
                        $"{record.Productsn ?? ""}".PadRight(18) +
                        $"{record.Weight:F2}".PadRight(8) +
                        $"{record.Weighterr:F2}".PadRight(8) +
                        $"{record.Pass ?? ""}".PadRight(6) +
                        $"{record.Jgno ?? ""}".PadRight(12) +
                        $"{record.Scperson ?? ""}".PadRight(10) +
                        $"{record.Weightdate ?? ""}"
                    );
                }

                if (weightRecords.Count > 20)
                {
                    Console.WriteLine($"   ... 还有 {weightRecords.Count - 20} 条记录未显示");
                }

                // 统计信息
                var passCount = weightRecords.Count(x => x.Pass == "PASS");
                var ngCount = weightRecords.Count(x => x.Pass == "NG");
                var avgWeight = weightRecords.Where(x => x.Weight > 0).Average(x => x.Weight);
                
                Console.WriteLine();
                Console.WriteLine($"📊 统计信息:");
                Console.WriteLine($"   • 合格数量: {passCount} 条");
                Console.WriteLine($"   • 不合格数量: {ngCount} 条");
                Console.WriteLine($"   • 合格率: {(passCount * 100.0 / weightRecords.Count):F1}%");
                Console.WriteLine($"   • 平均重量: {avgWeight:F2}");
                
                // 按日期统计
                var dateGroups = weightRecords
                    .Where(x => !string.IsNullOrEmpty(x.Weightdate))
                    .GroupBy(x => x.Weightdate?.Substring(0, 10)) // 取日期部分
                    .OrderByDescending(g => g.Key)
                    .Take(10);
                
                Console.WriteLine($"   • 最近10天记录数:");
                foreach (var group in dateGroups)
                {
                    Console.WriteLine($"     {group.Key}: {group.Count()} 条");
                }
                
                Console.WriteLine();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 读取称重记录时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 查看产品信息
        /// </summary>
        private static void ViewProductRecords(LiteDatabase db)
        {
            try
            {
                var products = db.GetCollection<ProductItem>("products");
                var productRecords = products.FindAll().ToList();

                Console.WriteLine($"📦 === 产品信息 (products) - 共 {productRecords.Count} 条记录 ===");
                
                if (productRecords.Count == 0)
                {
                    Console.WriteLine("   暂无产品信息");
                    Console.WriteLine();
                    return;
                }

                // 表头
                Console.WriteLine("ID".PadRight(5) + 
                                "产品编码".PadRight(12) + 
                                "订货号".PadRight(12) + 
                                "型号".PadRight(15) + 
                                "标准重量".PadRight(10) + 
                                "上限".PadRight(8) + 
                                "下限".PadRight(8) + 
                                "SN长度".PadRight(8) + 
                                "创建时间");
                Console.WriteLine(new string('─', 100));

                foreach (var record in productRecords)
                {
                    Console.WriteLine(
                        $"{record.Id}".PadRight(5) +
                        $"{record.Productcode ?? ""}".PadRight(12) +
                        $"{record.Ordernum ?? ""}".PadRight(12) +
                        $"{record.Model ?? ""}".PadRight(15) +
                        $"{record.Weight:F2}".PadRight(10) +
                        $"{record.Maxweight:F2}".PadRight(8) +
                        $"{record.Minweight:F2}".PadRight(8) +
                        $"{record.Snnum}".PadRight(8) +
                        $"{record.Sndate ?? ""}"
                    );
                }
                Console.WriteLine();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 读取产品信息时出错: {ex.Message}");
            }
        }

        /// <summary>
        /// 搜索特定产品的称重记录
        /// </summary>
        public static void SearchWeightRecords(string dbPath, string searchTerm, string searchType = "sn")
        {
            try
            {
                using (var db = new LiteDatabase(dbPath))
                {
                    var weights = db.GetCollection<WeightItem>("weights");
                    IEnumerable<WeightItem> results = new List<WeightItem>();

                    switch (searchType.ToLower())
                    {
                        case "sn":
                            results = weights.Find(Query.Contains("Productsn", searchTerm));
                            break;
                        case "model":
                            results = weights.Find(Query.Contains("Model", searchTerm));
                            break;
                        case "code":
                            results = weights.Find(Query.Contains("Productcode", searchTerm));
                            break;
                        case "jg":
                            results = weights.Find(Query.Contains("Jgno", searchTerm));
                            break;
                        case "person":
                            results = weights.Find(Query.Contains("Scperson", searchTerm));
                            break;
                    }

                    var resultList = results.ToList();
                    Console.WriteLine($"🔍 搜索结果: 找到 {resultList.Count} 条记录 (搜索条件: {searchType} = {searchTerm})");
                    
                    if (resultList.Count > 0)
                    {
                        Console.WriteLine();
                        foreach (var record in resultList.Take(10))
                        {
                            Console.WriteLine($"ID: {record.Id}, SN: {record.Productsn}, 重量: {record.Weight:F2}, 结果: {record.Pass}, 时间: {record.Weightdate}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 搜索时出错: {ex.Message}");
            }
        }
    }
}
