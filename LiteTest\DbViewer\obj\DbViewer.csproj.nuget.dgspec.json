{"format": 1, "restore": {"I:\\重量\\LiteTest\\LiteTest\\LiteTest\\DbViewer\\DbViewer.csproj": {}}, "projects": {"I:\\重量\\LiteTest\\LiteTest\\LiteTest\\DbViewer\\DbViewer.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "I:\\重量\\LiteTest\\LiteTest\\LiteTest\\DbViewer\\DbViewer.csproj", "projectName": "DbViewer", "projectPath": "I:\\重量\\LiteTest\\LiteTest\\LiteTest\\DbViewer\\DbViewer.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "I:\\重量\\LiteTest\\LiteTest\\LiteTest\\DbViewer\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"LiteDB": {"target": "Package", "version": "[5.0.21, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.413/PortableRuntimeIdentifierGraph.json"}}}}}