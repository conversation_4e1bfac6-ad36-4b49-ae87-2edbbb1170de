using System;
using System.IO;

namespace LiteTest
{
    /// <summary>
    /// 数据库读取器主程序
    /// </summary>
    class DbReader
    {
        static void Main(string[] args)
        {
            Console.OutputEncoding = System.Text.Encoding.UTF8;
            Console.WriteLine("🗄️  LiteDB 数据库查看器");
            Console.WriteLine("========================");
            Console.WriteLine();

            // 默认数据库文件路径
            string defaultDbPath = @"productdata-log_2025年08月13日15时51分58秒.db";
            
            if (args.Length > 0)
            {
                // 如果提供了命令行参数，使用第一个参数作为数据库路径
                defaultDbPath = args[0];
            }

            while (true)
            {
                Console.WriteLine("请选择操作:");
                Console.WriteLine("1. 查看数据库内容");
                Console.WriteLine("2. 搜索称重记录");
                Console.WriteLine("3. 指定数据库文件路径");
                Console.WriteLine("4. 退出");
                Console.Write("请输入选项 (1-4): ");

                var choice = Console.ReadLine();

                switch (choice)
                {
                    case "1":
                        ViewDatabase(defaultDbPath);
                        break;
                    case "2":
                        SearchRecords(defaultDbPath);
                        break;
                    case "3":
                        defaultDbPath = SetDatabasePath();
                        break;
                    case "4":
                        Console.WriteLine("再见!");
                        return;
                    default:
                        Console.WriteLine("❌ 无效选项，请重新选择。");
                        break;
                }

                Console.WriteLine();
                Console.WriteLine("按任意键继续...");
                Console.ReadKey();
                Console.Clear();
            }
        }

        /// <summary>
        /// 查看数据库内容
        /// </summary>
        static void ViewDatabase(string dbPath)
        {
            Console.Clear();
            Console.WriteLine("📊 查看数据库内容");
            Console.WriteLine("==================");
            Console.WriteLine();

            DatabaseViewer.ViewDatabase(dbPath);
        }

        /// <summary>
        /// 搜索记录
        /// </summary>
        static void SearchRecords(string dbPath)
        {
            Console.Clear();
            Console.WriteLine("🔍 搜索称重记录");
            Console.WriteLine("================");
            Console.WriteLine();

            Console.WriteLine("请选择搜索类型:");
            Console.WriteLine("1. 按序列号 (SN) 搜索");
            Console.WriteLine("2. 按产品型号搜索");
            Console.WriteLine("3. 按产品编码搜索");
            Console.WriteLine("4. 按加工单号搜索");
            Console.WriteLine("5. 按操作员搜索");
            Console.Write("请选择 (1-5): ");

            var searchTypeChoice = Console.ReadLine();
            string searchType = "";

            switch (searchTypeChoice)
            {
                case "1":
                    searchType = "sn";
                    break;
                case "2":
                    searchType = "model";
                    break;
                case "3":
                    searchType = "code";
                    break;
                case "4":
                    searchType = "jg";
                    break;
                case "5":
                    searchType = "person";
                    break;
                default:
                    Console.WriteLine("❌ 无效选项");
                    return;
            }

            Console.Write("请输入搜索关键词: ");
            var searchTerm = Console.ReadLine();

            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                Console.WriteLine("❌ 搜索关键词不能为空");
                return;
            }

            Console.WriteLine();
            DatabaseViewer.SearchWeightRecords(dbPath, searchTerm, searchType);
        }

        /// <summary>
        /// 设置数据库文件路径
        /// </summary>
        static string SetDatabasePath()
        {
            Console.Clear();
            Console.WriteLine("📁 设置数据库文件路径");
            Console.WriteLine("======================");
            Console.WriteLine();

            Console.WriteLine("当前支持的数据库文件:");
            Console.WriteLine("1. productdata-log_2025年08月13日15时51分58秒.db (当前目录)");
            Console.WriteLine("2. 自定义路径");
            Console.Write("请选择 (1-2): ");

            var choice = Console.ReadLine();

            switch (choice)
            {
                case "1":
                    return @"productdata-log_2025年08月13日15时51分58秒.db";
                case "2":
                    Console.Write("请输入数据库文件的完整路径: ");
                    var customPath = Console.ReadLine();
                    
                    if (string.IsNullOrWhiteSpace(customPath))
                    {
                        Console.WriteLine("❌ 路径不能为空，使用默认路径");
                        return @"productdata-log_2025年08月13日15时51分58秒.db";
                    }

                    if (!File.Exists(customPath))
                    {
                        Console.WriteLine($"⚠️  警告: 文件不存在 - {customPath}");
                        Console.WriteLine("但路径已设置，您可以稍后将文件复制到该位置。");
                    }
                    else
                    {
                        Console.WriteLine($"✅ 数据库文件路径已设置: {customPath}");
                    }

                    return customPath;
                default:
                    Console.WriteLine("❌ 无效选项，使用默认路径");
                    return @"productdata-log_2025年08月13日15时51分58秒.db";
            }
        }
    }
}
