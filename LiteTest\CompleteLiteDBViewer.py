#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的 LiteDB 数据库查看器
支持完整的数据结构读取和分析
"""

import os
import sys
import json
from datetime import datetime

def install_pylitedb():
    """安装 pylitedb 包"""
    try:
        import subprocess
        print("🔧 正在安装 pylitedb...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pylitedb"])
        print("✅ pylitedb 安装成功!")
        return True
    except Exception as e:
        print(f"❌ 安装 pylitedb 失败: {e}")
        return False

def try_import_litedb():
    """尝试导入 LiteDB 相关模块"""
    try:
        # 尝试导入 pylitedb
        import litedb
        return litedb, "pylitedb"
    except ImportError:
        try:
            # 尝试导入其他 LiteDB Python 绑定
            import pylitedb as litedb
            return litedb, "pylitedb"
        except ImportError:
            print("❌ 未找到 LiteDB Python 绑定")
            print("正在尝试安装 pylitedb...")
            if install_pylitedb():
                try:
                    import litedb
                    return litedb, "pylitedb"
                except ImportError:
                    pass
            return None, None

def read_with_alternative_method(db_path):
    """使用替代方法读取 LiteDB"""
    print("🔄 使用替代方法读取数据库...")
    
    # 方法1: 尝试使用 sqlite3 (某些情况下 LiteDB 兼容)
    try:
        import sqlite3
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取表信息
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        if tables:
            print("✅ 使用 SQLite 兼容模式读取成功")
            for table in tables:
                print(f"📋 表: {table[0]}")
                cursor.execute(f"SELECT COUNT(*) FROM {table[0]}")
                count = cursor.fetchone()[0]
                print(f"   记录数: {count}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ SQLite 兼容模式失败: {e}")
    
    # 方法2: 二进制分析 (更详细版本)
    return read_binary_analysis(db_path)

def read_binary_analysis(db_path):
    """二进制分析方法"""
    print("🔍 使用二进制分析方法...")
    
    try:
        with open(db_path, 'rb') as f:
            content = f.read()
        
        # LiteDB 文件头分析
        if content[:8] == b'LiteDB\x00\x00':
            print("✅ 确认为 LiteDB 格式文件")
        
        # 转换为文本进行分析
        text_content = content.decode('utf-8', errors='ignore')
        
        # 查找 JSON 结构
        import re
        
        # 查找可能的 JSON 对象
        json_patterns = re.findall(r'\{[^{}]*"[^"]*"[^{}]*\}', text_content)
        
        print(f"📊 找到 {len(json_patterns)} 个可能的数据对象")
        
        # 分析数据结构
        analyze_data_structure(text_content)
        
        return True
        
    except Exception as e:
        print(f"❌ 二进制分析失败: {e}")
        return False

def analyze_data_structure(content):
    """分析数据结构"""
    import re
    
    print("\n🔍 === 数据结构分析 ===")
    
    # 查找字段名
    field_patterns = [
        r'"(\w+)"\s*:\s*"([^"]*)"',  # 字符串字段
        r'"(\w+)"\s*:\s*(\d+\.?\d*)',  # 数字字段
        r'"(\w+)"\s*:\s*(true|false)',  # 布尔字段
    ]
    
    all_fields = {}
    
    for pattern in field_patterns:
        matches = re.findall(pattern, content)
        for field, value in matches:
            if field not in all_fields:
                all_fields[field] = []
            all_fields[field].append(value)
    
    # 显示字段统计
    print("📋 发现的字段:")
    for field, values in all_fields.items():
        unique_values = len(set(values))
        total_values = len(values)
        print(f"   • {field}: {total_values} 个值, {unique_values} 个唯一值")
        
        # 显示示例值
        if unique_values <= 5:
            print(f"     示例: {', '.join(set(values))}")
        else:
            print(f"     示例: {', '.join(list(set(values))[:3])}...")

def read_litedb_complete(db_path):
    """完整读取 LiteDB 数据库"""
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    file_size = os.path.getsize(db_path)
    print(f"🗄️  完整 LiteDB 数据库查看器")
    print(f"================================")
    print(f"📁 文件路径: {db_path}")
    print(f"📊 文件大小: {file_size / 1024:.2f} KB")
    print(f"📅 修改时间: {datetime.fromtimestamp(os.path.getmtime(db_path))}")
    print()
    
    # 尝试导入 LiteDB 模块
    litedb, method = try_import_litedb()
    
    if litedb:
        print(f"✅ 使用 {method} 读取数据库")
        return read_with_litedb(db_path, litedb)
    else:
        print("⚠️  未找到 LiteDB Python 绑定，使用替代方法")
        return read_with_alternative_method(db_path)

def read_with_litedb(db_path, litedb):
    """使用 LiteDB 模块读取"""
    try:
        # 打开数据库
        db = litedb.LiteDatabase(db_path)
        
        # 获取所有集合
        collections = db.get_collection_names()
        print(f"📋 数据库集合 (共 {len(collections)} 个):")
        
        for collection_name in collections:
            collection = db.get_collection(collection_name)
            count = collection.count()
            print(f"   • {collection_name}: {count} 条记录")
            
            # 读取前几条记录查看结构
            if count > 0:
                print(f"     📄 {collection_name} 数据结构:")
                records = collection.find_all().limit(3)
                
                for i, record in enumerate(records, 1):
                    print(f"       记录 {i}:")
                    for key, value in record.items():
                        print(f"         {key}: {value}")
                    if i >= 2:  # 只显示前2条
                        break
                print()
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ 使用 LiteDB 模块读取失败: {e}")
        return False

def export_to_json(db_path, output_path="database_export.json"):
    """导出数据库到 JSON 文件"""
    print(f"📤 导出数据到 {output_path}...")
    
    # 这里可以实现导出逻辑
    # 由于没有完整的 LiteDB 绑定，暂时使用文本解析
    
    try:
        with open(db_path, 'rb') as f:
            content = f.read()
        
        text_content = content.decode('utf-8', errors='ignore')
        
        # 提取数据并保存为 JSON
        export_data = {
            "file_info": {
                "path": db_path,
                "size": len(content),
                "export_time": datetime.now().isoformat()
            },
            "extracted_data": {
                "text_length": len(text_content),
                "contains_litedb_header": content[:8] == b'LiteDB\x00\x00'
            }
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        print(f"✅ 数据已导出到 {output_path}")
        return True
        
    except Exception as e:
        print(f"❌ 导出失败: {e}")
        return False

def main():
    """主函数"""
    
    # 数据库文件路径
    db_path = "productdata-log_2025年08月13日15时51分58秒.db"
    
    # 检查文件路径
    if not os.path.exists(db_path):
        possible_paths = [
            "../productdata-log_2025年08月13日15时51分58秒.db",
            "../../productdata-log_2025年08月13日15时51分58秒.db",
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                db_path = path
                break
        else:
            print("❌ 找不到数据库文件")
            print("请确保数据库文件在当前目录或上级目录中")
            return
    
    # 读取数据库
    success = read_litedb_complete(db_path)
    
    if success:
        print("\n" + "="*50)
        print("✅ 数据库分析完成")
        
        # 询问是否导出
        try:
            export_choice = input("\n是否导出数据到 JSON 文件? (y/n): ").lower()
            if export_choice in ['y', 'yes', '是']:
                export_to_json(db_path)
        except KeyboardInterrupt:
            pass
    
    print("\n按 Enter 键退出...")
    try:
        input()
    except KeyboardInterrupt:
        pass

if __name__ == "__main__":
    main()
