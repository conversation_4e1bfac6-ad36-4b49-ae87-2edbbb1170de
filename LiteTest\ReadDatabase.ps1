# PowerShell 脚本：读取 LiteDB 数据库内容
# 使用方法：在 PowerShell 中运行 .\ReadDatabase.ps1

Write-Host "🗄️  LiteDB 数据库查看器" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan
Write-Host ""

# 数据库文件路径
$dbPath = "productdata-log_2025年08月13日15时51分58秒.db"

# 检查文件是否存在
if (-not (Test-Path $dbPath)) {
    Write-Host "❌ 数据库文件不存在: $dbPath" -ForegroundColor Red
    Write-Host "请确认文件路径是否正确。" -ForegroundColor Yellow
    Read-Host "按 Enter 键退出"
    exit
}

# 获取文件信息
$fileInfo = Get-Item $dbPath
Write-Host "📁 文件路径: $($fileInfo.FullName)" -ForegroundColor Green
Write-Host "📊 文件大小: $([math]::Round($fileInfo.Length / 1KB, 2)) KB" -ForegroundColor Green
Write-Host "📅 修改时间: $($fileInfo.LastWriteTime)" -ForegroundColor Green
Write-Host ""

# 尝试加载 LiteDB 程序集
try {
    # 查找 LiteDB.dll
    $liteDbPath = Get-ChildItem -Path "." -Recurse -Name "LiteDB.dll" | Select-Object -First 1
    
    if ($liteDbPath) {
        Write-Host "✅ 找到 LiteDB.dll: $liteDbPath" -ForegroundColor Green
        Add-Type -Path $liteDbPath
        Write-Host "✅ LiteDB 程序集加载成功" -ForegroundColor Green
    } else {
        Write-Host "❌ 未找到 LiteDB.dll，请确保项目已编译" -ForegroundColor Red
        Write-Host "请先在 Visual Studio 中编译项目，或运行 'dotnet build'" -ForegroundColor Yellow
        Read-Host "按 Enter 键退出"
        exit
    }
} catch {
    Write-Host "❌ 加载 LiteDB 程序集失败: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "按 Enter 键退出"
    exit
}

try {
    # 打开数据库
    $db = New-Object LiteDB.LiteDatabase($dbPath)
    Write-Host "✅ 数据库连接成功!" -ForegroundColor Green
    Write-Host ""

    # 获取所有集合名称
    $collections = $db.GetCollectionNames()
    Write-Host "📋 数据库中的集合:" -ForegroundColor Cyan
    foreach ($collection in $collections) {
        $count = $db.GetCollection($collection).Count()
        Write-Host "   • $collection ($count 条记录)" -ForegroundColor White
    }
    Write-Host ""

    # 读取称重记录
    if ($collections -contains "weights") {
        Write-Host "⚖️  === 称重记录 (weights) ===" -ForegroundColor Yellow
        $weights = $db.GetCollection("weights")
        $weightRecords = $weights.FindAll() | Sort-Object Id -Descending | Select-Object -First 10
        
        Write-Host "显示最新的 10 条记录:" -ForegroundColor Gray
        Write-Host "ID`t产品编码`t型号`t`t序列号`t`t重量`t结果`t时间" -ForegroundColor White
        Write-Host ("-" * 80) -ForegroundColor Gray
        
        foreach ($record in $weightRecords) {
            $id = $record["_id"]
            $productcode = $record["Productcode"]
            $model = $record["Model"]
            $productsn = $record["Productsn"]
            $weight = $record["Weight"]
            $pass = $record["Pass"]
            $weightdate = $record["Weightdate"]
            
            Write-Host "$id`t$productcode`t$model`t$productsn`t$weight`t$pass`t$weightdate" -ForegroundColor White
        }
        Write-Host ""
    }

    # 读取产品信息
    if ($collections -contains "products") {
        Write-Host "📦 === 产品信息 (products) ===" -ForegroundColor Yellow
        $products = $db.GetCollection("products")
        $productRecords = $products.FindAll()
        
        Write-Host "ID`t产品编码`t型号`t`t标准重量`t上限`t下限" -ForegroundColor White
        Write-Host ("-" * 60) -ForegroundColor Gray
        
        foreach ($record in $productRecords) {
            $id = $record["_id"]
            $productcode = $record["Productcode"]
            $model = $record["Model"]
            $weight = $record["Weight"]
            $maxweight = $record["Maxweight"]
            $minweight = $record["Minweight"]
            
            Write-Host "$id`t$productcode`t$model`t$weight`t$maxweight`t$minweight" -ForegroundColor White
        }
        Write-Host ""
    }

    # 关闭数据库
    $db.Dispose()
    Write-Host "✅ 数据库读取完成" -ForegroundColor Green

} catch {
    Write-Host "❌ 读取数据库时发生错误: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "详细错误: $($_.Exception)" -ForegroundColor Red
} finally {
    if ($db) {
        $db.Dispose()
    }
}

Write-Host ""
Read-Host "按 Enter 键退出"
