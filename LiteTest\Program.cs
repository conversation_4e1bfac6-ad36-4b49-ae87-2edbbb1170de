using LiteDB;
using System;
using System.IO;
using System.Linq;
using Weightdb;

namespace LiteTest
{
  internal static class Program
  {
    /// <summary>
    ///  The main entry point for the application.
    /// </summary>
    [STAThread]
    static void Main(string[] args)
    {
      // 如果有命令行参数且第一个参数是 "readdb"，则读取数据库
      if (args.Length > 0 && args[0] == "readdb")
      {
        ReadDatabase();
        return;
      }

      // To customize application configuration such as set high DPI settings or default font,
      // see https://aka.ms/applicationconfiguration.
      ApplicationConfiguration.Initialize();
      Application.Run(new Form1());
    }

    /// <summary>
    /// 读取数据库内容的方法
    /// </summary>
    static void ReadDatabase()
    {
      try
      {
        // 数据库文件路径
        string dbPath = @"productdata-log_2025年08月13日15时51分58秒.db";

        // 检查文件是否存在
        if (!File.Exists(dbPath))
        {
          Console.WriteLine($"数据库文件不存在: {dbPath}");
          Console.WriteLine("请确认文件路径是否正确。");
          Console.ReadKey();
          return;
        }

        using (var db = new LiteDB.LiteDatabase(dbPath))
        {
          Console.WriteLine("=== 数据库连接成功 ===");
          Console.WriteLine($"数据库文件: {dbPath}");
          Console.WriteLine();

          // 获取所有集合名称
          var collections = db.GetCollectionNames();
          Console.WriteLine("数据库中的集合:");
          foreach (var collection in collections)
          {
            Console.WriteLine($"- {collection}");
          }
          Console.WriteLine();

          // 读取 weights 集合（称重记录）
          if (collections.Contains("weights"))
          {
            var weights = db.GetCollection<Weightdb.WeightItem>("weights");
            var weightRecords = weights.FindAll().ToList();

            Console.WriteLine($"=== 称重记录 (weights) - 共 {weightRecords.Count} 条记录 ===");
            Console.WriteLine("ID\t产品编码\t型号\t\t序列号\t\t\t重量\t误差\t结果\t加工单号\t操作员\t称重时间");
            Console.WriteLine(new string('-', 120));

            foreach (var record in weightRecords.Take(20)) // 只显示前20条
            {
              Console.WriteLine($"{record.Id}\t{record.Productcode}\t{record.Model}\t{record.Productsn}\t{record.Weight:F2}\t{record.Weighterr:F2}\t{record.Pass}\t{record.Jgno}\t{record.Scperson}\t{record.Weightdate}");
            }

            if (weightRecords.Count > 20)
            {
              Console.WriteLine($"... 还有 {weightRecords.Count - 20} 条记录");
            }
            Console.WriteLine();
          }

          // 读取 products 集合（产品信息）
          if (collections.Contains("products"))
          {
            var products = db.GetCollection<LiteTest.Lite.ProductItem>("products");
            var productRecords = products.FindAll().ToList();

            Console.WriteLine($"=== 产品信息 (products) - 共 {productRecords.Count} 条记录 ===");
            Console.WriteLine("ID\t产品编码\t订货号\t型号\t\t标准重量\t上限\t下限\tSN长度\t创建时间");
            Console.WriteLine(new string('-', 100));

            foreach (var record in productRecords)
            {
              Console.WriteLine($"{record.Id}\t{record.Productcode}\t{record.Ordernum}\t{record.Model}\t{record.Weight:F2}\t{record.Maxweight:F2}\t{record.Minweight:F2}\t{record.Snnum}\t{record.Sndate}");
            }
            Console.WriteLine();
          }

          Console.WriteLine("=== 数据读取完成 ===");
        }
      }
      catch (Exception ex)
      {
        Console.WriteLine($"读取数据库时发生错误: {ex.Message}");
        Console.WriteLine($"详细错误: {ex}");
      }

      Console.WriteLine("按任意键退出...");
      Console.ReadKey();
    }
  }
}