using LiteDB;

namespace Weightdb
{
    public class WeightItem
    {
        [BsonId]
        public int Id { get; set; }//序号，自动生成
        public string? Productcode { get; set; }//产品编码
        public string? Model { get; set; }//产品型号
        public string? Productsn { get; set; }//产品序列号
        public double Weight { get; set; }//实测重量
        public double? Weighterr { get; set; }//重量误差
        public string? Pass { get; set; }//是否合格
        public string? Jgno { get; set; }//加工单号
        public string? Scperson { get; set; }//操作人
        public string? Weightdate { get; set; }//称重时间
    }
}
