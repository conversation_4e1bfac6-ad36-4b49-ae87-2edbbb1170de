#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细的 LiteDB 数据库分析器
提供更深入的数据分析和统计
"""

import os
import re
import json
from datetime import datetime
from collections import Counter, defaultdict

def analyze_weight_data(content):
    """分析称重数据的详细信息"""
    
    print("📊 === 详细称重数据分析 ===")
    
    # 提取所有称重记录的详细信息
    weight_pattern = r'Weight\s*([0-9]+\.?[0-9]*)'
    weights = [float(w) for w in re.findall(weight_pattern, content)]
    
    # 提取误差数据
    error_pattern = r'Weighterr\s*([+-]?[0-9]*\.?[0-9]*)'
    errors = [float(e) for e in re.findall(error_pattern, content) if e and e != '0']
    
    # 提取产品序列号和对应重量
    sn_weight_pattern = r'Productsn\s*([A-Z0-9]+).*?Weight\s*([0-9]+\.?[0-9]*)'
    sn_weights = re.findall(sn_weight_pattern, content, re.DOTALL)
    
    if weights:
        print(f"⚖️  重量统计:")
        print(f"   • 总记录数: {len(weights)}")
        print(f"   • 平均重量: {sum(weights)/len(weights):.3f}")
        print(f"   • 最大重量: {max(weights):.3f}")
        print(f"   • 最小重量: {min(weights):.3f}")
        print(f"   • 重量范围: {max(weights) - min(weights):.3f}")
        
        # 重量分布分析
        weight_ranges = {
            "< 50": len([w for w in weights if w < 50]),
            "50-100": len([w for w in weights if 50 <= w < 100]),
            "100-150": len([w for w in weights if 100 <= w < 150]),
            "150-200": len([w for w in weights if 150 <= w < 200]),
            "> 200": len([w for w in weights if w >= 200])
        }
        
        print(f"   • 重量分布:")
        for range_name, count in weight_ranges.items():
            if count > 0:
                percentage = (count / len(weights)) * 100
                print(f"     {range_name}: {count} 条 ({percentage:.1f}%)")
    
    if errors:
        print(f"\n📏 误差统计:")
        print(f"   • 有误差记录: {len(errors)}")
        print(f"   • 平均误差: {sum(errors)/len(errors):.3f}")
        print(f"   • 最大误差: {max(errors):.3f}")
        print(f"   • 最小误差: {min(errors):.3f}")
    
    # 分析每个产品的重量分布
    if sn_weights:
        product_weights = defaultdict(list)
        for sn, weight in sn_weights:
            product_code = sn[:4]  # 取前4位作为产品代码
            product_weights[product_code].append(float(weight))
        
        print(f"\n📦 各产品重量分析:")
        for product, weights_list in product_weights.items():
            if len(weights_list) > 5:  # 只显示有足够数据的产品
                avg_weight = sum(weights_list) / len(weights_list)
                print(f"   • {product}: {len(weights_list)} 条记录, 平均重量: {avg_weight:.3f}")

def analyze_time_trends(content):
    """分析时间趋势"""
    
    print("\n📅 === 时间趋势分析 ===")
    
    # 提取日期和时间
    date_pattern = r'(\d{4}-\d{2}-\d{2})'
    time_pattern = r'(\d{2}:\d{2}:\d{2})'
    
    dates = re.findall(date_pattern, content)
    times = re.findall(time_pattern, content)
    
    if dates:
        date_counter = Counter(dates)
        print(f"📊 每日记录数量 (前10天):")
        for date, count in date_counter.most_common(10):
            print(f"   • {date}: {count} 条记录")
        
        # 按月统计
        months = [date[:7] for date in dates]  # YYYY-MM
        month_counter = Counter(months)
        print(f"\n📈 每月记录数量:")
        for month, count in sorted(month_counter.items()):
            print(f"   • {month}: {count} 条记录")
    
    if times:
        # 按小时统计工作时间分布
        hours = [time[:2] for time in times]
        hour_counter = Counter(hours)
        print(f"\n🕐 工作时间分布:")
        for hour in sorted(hour_counter.keys()):
            count = hour_counter[hour]
            bar = "█" * (count // 10) if count >= 10 else "▌" if count >= 5 else "▏"
            print(f"   • {hour}:00 - {count} 条 {bar}")

def analyze_quality_trends(content):
    """分析质量趋势"""
    
    print("\n✅ === 质量分析 ===")
    
    # 提取 PASS/NG 结果和对应的日期
    pass_pattern = r'Pass\s*(PASS|NG).*?Weightdate\s*(\d{4}-\d{2}-\d{2})'
    quality_dates = re.findall(pass_pattern, content, re.DOTALL)
    
    if quality_dates:
        # 按日期统计合格率
        daily_quality = defaultdict(lambda: {'PASS': 0, 'NG': 0})
        
        for result, date in quality_dates:
            daily_quality[date][result] += 1
        
        print(f"📊 每日质量统计 (最近10天):")
        sorted_dates = sorted(daily_quality.keys(), reverse=True)[:10]
        
        for date in sorted_dates:
            pass_count = daily_quality[date]['PASS']
            ng_count = daily_quality[date]['NG']
            total = pass_count + ng_count
            pass_rate = (pass_count / total * 100) if total > 0 else 0
            
            print(f"   • {date}: PASS {pass_count}, NG {ng_count}, 合格率 {pass_rate:.1f}%")

def analyze_operators(content):
    """分析操作员信息"""
    
    print("\n👤 === 操作员分析 ===")
    
    # 提取操作员和对应的记录
    operator_pattern = r'Scperson\s*([^\s\x00-\x1f]+?)(?:\s|Weightdate)'
    operators = re.findall(operator_pattern, content)
    
    if operators:
        operator_counter = Counter(operators)
        print(f"📊 操作员工作量统计:")
        for operator, count in operator_counter.most_common():
            if operator.strip() and len(operator.strip()) > 0:
                print(f"   • {operator.strip()}: {count} 条记录")

def export_analysis_report(db_path, analysis_data):
    """导出分析报告"""
    
    report_path = f"数据库分析报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    
    try:
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(analysis_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 分析报告已导出: {report_path}")
        return True
    except Exception as e:
        print(f"❌ 导出报告失败: {e}")
        return False

def main():
    """主函数"""
    
    # 数据库文件路径
    db_path = "productdata-log_2025年08月13日15时51分58秒.db"
    
    # 检查文件路径
    if not os.path.exists(db_path):
        possible_paths = [
            "../productdata-log_2025年08月13日15时51分58秒.db",
            "../../productdata-log_2025年08月13日15时51分58秒.db",
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                db_path = path
                break
        else:
            print("❌ 找不到数据库文件")
            return
    
    file_size = os.path.getsize(db_path)
    print(f"🔍 详细数据库分析器")
    print(f"====================")
    print(f"📁 文件: {db_path}")
    print(f"📊 大小: {file_size / 1024:.2f} KB")
    print(f"📅 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # 读取文件内容
        with open(db_path, 'rb') as f:
            content = f.read()
        
        text_content = content.decode('utf-8', errors='ignore')
        
        # 执行各种分析
        analyze_weight_data(text_content)
        analyze_time_trends(text_content)
        analyze_quality_trends(text_content)
        analyze_operators(text_content)
        
        # 准备导出数据
        analysis_data = {
            "file_info": {
                "path": db_path,
                "size_kb": file_size / 1024,
                "analysis_time": datetime.now().isoformat()
            },
            "summary": "详细分析完成"
        }
        
        print("\n" + "="*50)
        print("✅ 详细分析完成")
        
        # 询问是否导出报告
        try:
            export_choice = input("\n是否导出详细分析报告? (y/n): ").lower()
            if export_choice in ['y', 'yes', '是']:
                export_analysis_report(db_path, analysis_data)
        except KeyboardInterrupt:
            pass
        
    except Exception as e:
        print(f"❌ 分析过程中出错: {e}")
    
    print("\n按 Enter 键退出...")
    try:
        input()
    except KeyboardInterrupt:
        pass

if __name__ == "__main__":
    main()
