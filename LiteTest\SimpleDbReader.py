#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的 LiteDB 数据库内容查看器
使用 Python 读取 LiteDB 文件的文本内容
"""

import os
import re
import json
from datetime import datetime

def read_litedb_content(db_path):
    """读取 LiteDB 文件内容"""
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    file_size = os.path.getsize(db_path)
    print(f"🗄️  LiteDB 数据库查看器")
    print(f"========================")
    print(f"📁 文件路径: {db_path}")
    print(f"📊 文件大小: {file_size / 1024:.2f} KB")
    print()
    
    try:
        # 以二进制模式读取文件
        with open(db_path, 'rb') as f:
            content = f.read()
        
        # 转换为字符串，忽略无法解码的字符
        text_content = content.decode('utf-8', errors='ignore')
        
        # 查找称重记录
        print("⚖️  === 称重记录分析 ===")
        find_weight_records(text_content)
        
        print("\n📦 === 产品信息分析 ===")
        find_product_records(text_content)
        
    except Exception as e:
        print(f"❌ 读取文件时出错: {e}")

def find_weight_records(content):
    """查找称重记录"""
    
    # 查找产品编码模式 (如 PJ01)
    product_codes = re.findall(r'PJ\d+', content)
    
    # 查找序列号模式 (如 PJ0125320044)
    serial_numbers = re.findall(r'PJ\d{10}', content)
    
    # 查找重量数据 (浮点数)
    weights = re.findall(r'Weight[^0-9]*([0-9]+\.?[0-9]*)', content)
    
    # 查找 PASS/NG 结果
    pass_results = re.findall(r'(PASS|NG)', content)
    
    # 查找日期时间
    dates = re.findall(r'(\d{4}-\d{2}-\d{2})', content)
    times = re.findall(r'(\d{2}:\d{2}:\d{2})', content)
    
    # 查找操作员姓名 (中文字符)
    operators = re.findall(r'Scperson[^a-zA-Z]*([^\x00-\x7F]+)', content)
    
    print(f"📊 统计信息:")
    print(f"   • 找到产品编码: {len(set(product_codes))} 种")
    print(f"   • 找到序列号: {len(serial_numbers)} 个")
    print(f"   • 找到重量记录: {len(weights)} 条")
    print(f"   • PASS 记录: {pass_results.count('PASS')} 条")
    print(f"   • NG 记录: {pass_results.count('NG')} 条")
    print(f"   • 找到日期: {len(set(dates))} 个")
    print(f"   • 找到操作员: {len(set(operators))} 个")
    
    if serial_numbers:
        print(f"\n📋 最近的序列号 (前10个):")
        for i, sn in enumerate(serial_numbers[-10:], 1):
            print(f"   {i:2d}. {sn}")
    
    if operators:
        print(f"\n👤 操作员列表:")
        for i, op in enumerate(set(operators), 1):
            print(f"   {i}. {op}")
    
    if dates:
        print(f"\n📅 记录日期:")
        for date in sorted(set(dates)):
            print(f"   • {date}")

def find_product_records(content):
    """查找产品信息"""
    
    # 查找产品型号
    models = re.findall(r'Model[^a-zA-Z]*([^Productsn]+?)Productsn', content)
    
    # 查找标准重量
    standard_weights = re.findall(r'Weight[^0-9]*([0-9]+\.?[0-9]*)', content)
    
    # 查找重量上下限
    max_weights = re.findall(r'Maxweight[^0-9]*([0-9]+\.?[0-9]*)', content)
    min_weights = re.findall(r'Minweight[^0-9]*([0-9]+\.?[0-9]*)', content)
    
    print(f"📊 产品信息统计:")
    print(f"   • 产品型号: {len(set(models))} 种")
    print(f"   • 标准重量记录: {len(standard_weights)} 条")
    print(f"   • 重量上限记录: {len(max_weights)} 条")
    print(f"   • 重量下限记录: {len(min_weights)} 条")
    
    if models:
        print(f"\n📦 产品型号列表:")
        for i, model in enumerate(set(models), 1):
            clean_model = model.strip().replace('\x00', '').strip()
            if clean_model:
                print(f"   {i}. {clean_model}")

def extract_readable_strings(content, min_length=3):
    """提取可读字符串"""
    
    # 查找连续的可打印字符
    readable_strings = re.findall(r'[a-zA-Z0-9\u4e00-\u9fff\-:. ]{' + str(min_length) + ',}', content)
    
    # 过滤掉太短或无意义的字符串
    filtered_strings = []
    for s in readable_strings:
        s = s.strip()
        if len(s) >= min_length and not s.isspace():
            filtered_strings.append(s)
    
    return filtered_strings

def main():
    """主函数"""
    
    # 数据库文件路径
    db_path = "productdata-log_2025年08月13日15时51分58秒.db"
    
    # 如果当前目录没有，尝试其他可能的路径
    if not os.path.exists(db_path):
        possible_paths = [
            "../productdata-log_2025年08月13日15时51分58秒.db",
            "../../productdata-log_2025年08月13日15时51分58秒.db",
            "LiteTest/productdata-log_2025年08月13日15时51分58秒.db"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                db_path = path
                break
        else:
            print("❌ 找不到数据库文件，请确认文件路径")
            print("请将此脚本放在数据库文件所在目录，或修改 db_path 变量")
            return
    
    read_litedb_content(db_path)
    
    print("\n" + "="*50)
    print("✅ 分析完成")
    input("按 Enter 键退出...")

if __name__ == "__main__":
    main()
