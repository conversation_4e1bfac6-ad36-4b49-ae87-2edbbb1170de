@echo off
echo LiteDB Database Viewer
echo ======================
echo.

REM Check if .NET is installed
where dotnet >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo .NET SDK not found
    echo Please download and install .NET 8.0 SDK from:
    echo https://dotnet.microsoft.com/download
    echo.
    echo Or use Python version: python SimpleDbReader.py
    pause
    exit /b 1
)

echo .NET SDK found
echo Compiling database viewer...
echo.

REM Compile database viewer
dotnet build DatabaseViewer.csproj -c Release -o bin

if %ERRORLEVEL% NEQ 0 (
    echo Compilation failed
    echo Trying Python version...
    echo.
    python SimpleDbReader.py
    pause
    exit /b 1
)

echo Compilation successful
echo Starting database viewer...
echo.

REM Run database viewer
bin\DatabaseViewer.exe "productdata-log_2025年08月13日15时51分58秒.db"

echo.
echo Program finished
pause
